# PDF Chat Application using LangChain and RAG

A Streamlit-based application that allows you to upload PDF documents and chat with them using Retrieval-Augmented Generation (RAG) powered by LangChain and OpenRouter API.

## Features

- 📄 **PDF Upload**: Upload any PDF document
- 🔍 **Smart Text Processing**: Automatically extracts and chunks text from PDFs
- 🧠 **AI-Powered Chat**: Ask questions about your PDF content
- 💬 **Interactive Chat Interface**: Modern chat UI with message history
- 📚 **Source References**: View the source documents used for answers
- 🚀 **Fast Embeddings**: Uses HuggingFace sentence transformers for local embeddings
- 🔗 **OpenRouter Integration**: Leverages OpenRouter API for LLM responses

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd pdf-chat-openrouter
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure Environment Variables
Create a `.env` file in the root directory with your OpenRouter API key:
```env
OPENROUTER_API_KEY=your_openrouter_api_key_here
MODEL=openrouter/cypher-alpha:free
```

### 4. Run the Application
```bash
streamlit run app.py
```

Or use the provided runner script:
```bash
python run_app.py
```

The app will open in your browser at `http://localhost:8501`

## How to Use

1. **Upload a PDF**: Click the "Upload a PDF" button and select your document
2. **Wait for Processing**: The app will extract text and create embeddings (this may take a moment)
3. **Start Chatting**: Once processing is complete, use the chat interface to ask questions about your PDF
4. **View Sources**: Expand the "Source Documents" section to see which parts of the PDF were used to generate the answer

## Technical Architecture

### Components
- **Frontend**: Streamlit for the web interface
- **PDF Processing**: PyMuPDF for text extraction
- **Text Splitting**: LangChain's RecursiveCharacterTextSplitter
- **Embeddings**: HuggingFace sentence-transformers (all-MiniLM-L6-v2)
- **Vector Store**: FAISS for similarity search
- **LLM**: OpenRouter API (configurable model)
- **RAG Chain**: LangChain's RetrievalQA

### File Structure
```
pdf-chat-openrouter/
├── app.py              # Main Streamlit application
├── rag_chain.py        # RAG chain configuration
├── requirements.txt    # Python dependencies
├── run_app.py         # Application runner script
├── .env               # Environment variables
├── temp/              # Temporary PDF storage
└── README.md          # This file
```

## Configuration

### Supported Models
The application uses OpenRouter API and can work with various models. Update the `MODEL` in your `.env` file:
- `openrouter/cypher-alpha:free` (default, free tier)
- `mistralai/mistral-7b-instruct`
- `anthropic/claude-3-haiku`
- And many more available on OpenRouter

### Customization Options
You can modify these parameters in the code:
- **Chunk Size**: Default 1000 characters (in `app.py`)
- **Chunk Overlap**: Default 200 characters
- **Retrieval Count**: Default 3 documents (k=3)
- **Temperature**: Default 0.2 for consistent responses

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **API Key Issues**: Verify your OpenRouter API key is correct in `.env`

3. **PDF Processing Errors**: Ensure your PDF contains extractable text (not just images)

4. **Memory Issues**: For large PDFs, consider reducing chunk size or using a more powerful machine

### Performance Tips
- Use PDFs with clear, extractable text for best results
- Smaller PDFs (< 50 pages) process faster
- The first run may be slower due to model downloads

## Dependencies

Key packages used:
- `streamlit`: Web interface
- `langchain`: RAG framework
- `langchain-community`: Community integrations
- `langchain-openai`: OpenAI-compatible API support
- `pymupdf`: PDF text extraction
- `faiss-cpu`: Vector similarity search
- `sentence-transformers`: Text embeddings
- `python-dotenv`: Environment variable management

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.
