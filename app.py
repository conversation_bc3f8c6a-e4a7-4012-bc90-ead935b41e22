import streamlit as st
import os
from langchain_community.document_loaders import PyMuPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from rag_chain import create_rag_chain

st.set_page_config(page_title="PDF Chat using LangChain + OpenRouter")
st.title("🧠 PDF Chat using LangChain + OpenRouter")

# Create temp directory if it doesn't exist
if not os.path.exists("temp"):
    os.makedirs("temp")

uploaded_file = st.file_uploader("Upload a PDF", type="pdf")

if uploaded_file is not None:
    try:
        # Save the uploaded PDF to a local folder
        file_path = os.path.join("temp", uploaded_file.name)
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())

        # Load and split PDF into chunks
        with st.spinner("Loading and processing PDF..."):
            loader = PyMuPDFLoader(file_path)
            documents = loader.load()

            if not documents:
                st.error("Could not extract text from the PDF. Please try a different file.")
                st.stop()

            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
                separators=["\n\n", "\n", " ", ""]
            )
            chunks = text_splitter.split_documents(documents)

            st.info(f"PDF processed into {len(chunks)} chunks")

        # Create vectorstore
        with st.spinner("Creating embeddings..."):
            embeddings = HuggingFaceEmbeddings(
                model_name="sentence-transformers/all-MiniLM-L6-v2",
                model_kwargs={'device': 'cpu'}
            )
            vectorstore = FAISS.from_documents(chunks, embeddings)

        # Create retriever and RAG chain
        retriever = vectorstore.as_retriever(search_kwargs={"k": 3})
        chain = create_rag_chain(retriever)

        st.success("PDF uploaded and loaded successfully!")

        # Store the chain in session state to persist across reruns
        st.session_state.chain = chain
        st.session_state.pdf_loaded = True

    except Exception as e:
        st.error(f"Error processing PDF: {str(e)}")
        st.stop()

# Chat interface
if hasattr(st.session_state, 'pdf_loaded') and st.session_state.pdf_loaded:
    st.markdown("---")
    st.subheader("💬 Chat with your PDF")

    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Display chat messages from history on app rerun
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Accept user input
    if prompt := st.chat_input("Ask a question about the PDF:"):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})

        # Display user message in chat message container
        with st.chat_message("user"):
            st.markdown(prompt)

        # Generate assistant response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                try:
                    result = st.session_state.chain.invoke({"query": prompt})
                    response = result["result"]
                    st.markdown(response)

                    # Add assistant response to chat history
                    st.session_state.messages.append({"role": "assistant", "content": response})

                    # Show source documents if available
                    if "source_documents" in result and result["source_documents"]:
                        with st.expander("📚 Source Documents"):
                            for i, doc in enumerate(result["source_documents"]):
                                st.write(f"**Source {i+1}:**")
                                st.write(doc.page_content[:500] + "..." if len(doc.page_content) > 500 else doc.page_content)
                                if hasattr(doc, 'metadata') and doc.metadata:
                                    st.write(f"*Page: {doc.metadata.get('page', 'Unknown')}*")
                                st.write("---")

                except Exception as e:
                    error_msg = f"Error generating response: {str(e)}"
                    st.error(error_msg)
                    st.session_state.messages.append({"role": "assistant", "content": error_msg})

else:
    st.info("👆 Please upload a PDF file to start chatting!")