import streamlit as st
import os
from langchain_community.document_loaders import PyMuPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from rag_chain import create_rag_chain

st.set_page_config(page_title="PDF Chat using LangChain + OpenRouter")
st.title("🧠 PDF Chat using LangChain + OpenRouter")

uploaded_file = st.file_uploader("Upload a PDF", type="pdf")

if uploaded_file is not None:
    # Save the uploaded PDF to a local folder
    file_path = os.path.join("temp", uploaded_file.name)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())

    # Load and split PDF into chunks
    loader = PyMuPDFLoader(file_path)
    documents = loader.load()
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    chunks = text_splitter.split_documents(documents)

    # Create vectorstore
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")
    vectorstore = FAISS.from_documents(chunks, embeddings)

    # Create retriever and RAG chain
    retriever = vectorstore.as_retriever(search_kwargs={"k": 3})
    chain = create_rag_chain(retriever)

    st.success("PDF uploaded and loaded!")

    # User input for chat
    question = st.text_input("Ask a question about the PDF:")
    if question:
        result = chain.invoke({"query": question})
        st.write("### Answer:")
        st.write(result["result"])