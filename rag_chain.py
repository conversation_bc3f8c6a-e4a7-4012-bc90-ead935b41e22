from langchain_community.chat_models import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.chains import RetrievalQA
import os
from dotenv import load_dotenv

load_dotenv()

def create_rag_chain(retriever):
    """
    Create a RAG chain using OpenRouter API
    """
    try:
        # Use the model from .env file
        model_name = os.getenv("MODEL", "openrouter/cypher-alpha:free")

        llm = ChatOpenAI(
            model=model_name,
            openai_api_base="https://openrouter.ai/api/v1",
            openai_api_key=os.getenv("OPENROUTER_API_KEY"),
            temperature=0.2,
            max_tokens=1000,
        )

        # Create a custom prompt template for better responses
        prompt_template = """Use the following pieces of context to answer the question at the end.
        If you don't know the answer based on the context provided, just say that you don't know, don't try to make up an answer.
        Always provide a comprehensive answer based on the context.

        Context:
        {context}

        Question: {question}

        Answer:"""

        # Create the RetrievalQA chain
        chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=True,
            chain_type_kwargs={
                "prompt": ChatPromptTemplate.from_template(prompt_template)
            }
        )

        return chain

    except Exception as e:
        raise Exception(f"Error creating RAG chain: {str(e)}")
