#!/usr/bin/env python3
"""
Simple script to run the Streamlit PDF Chat app
"""
import subprocess
import sys
import os

def main():
    """Run the Streamlit app"""
    try:
        # Change to the app directory
        app_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(app_dir)
        
        print("Starting PDF Chat App...")
        print("The app will open in your default browser.")
        print("Press Ctrl+C to stop the app.")
        
        # Run streamlit
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"], check=True)
        
    except KeyboardInterrupt:
        print("\nApp stopped by user.")
    except subprocess.CalledProcessError as e:
        print(f"Error running the app: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
